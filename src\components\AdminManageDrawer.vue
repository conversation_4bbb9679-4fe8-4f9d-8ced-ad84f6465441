<template>
  <div>
    <!-- 管理员抽屉 -->
    <el-drawer
      title="管理员管理"
      :visible.sync="drawerVisible"
      direction="rtl"
      :size="drawerSize"
      :before-close="handleDrawerClose"
      custom-class="admin-drawer"
    >
      <div class="drawer-content">
        <h3 class="drawer-title">管理员功能</h3>
        <div class="drawer-menu">
          <div class="menu-item" @click="handleCreateAdmin">
            <i class="fas fa-user-plus"></i>
            <span>创建管理员</span>
          </div>
          <!-- <div class="menu-item" @click="handleManageAdmin">
            <i class="fas fa-users-cog"></i>
            <span>管理员列表</span>
          </div> -->
          <div class="menu-item" @click="handleSystemUpgrade">
            <i class="fas fa-upload"></i>
            <span>升级</span>
          </div>
          <div class="menu-item" @click="handleImportOrganization">
            <i class="fas fa-sitemap"></i>
            <span>导入组织</span>
          </div>
          <div class="menu-item" @click="handleImportRole">
            <i class="fas fa-user-tag"></i>
            <span>导入角色</span>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 升级对话框 -->
    <el-dialog
      title="系统升级"
      :visible.sync="upgradeDialogVisible"
      :width="dialogWidth"
      custom-class="import-dialog"
    >
      <div class="import-container">
        <div class="import-btn-container text-center mb-4">
          <el-button
            type="primary"
            @click="selectUpgradeFile"
            class="import-select-btn"
          >
            <i class="el-icon-folder-opened mr-2"></i>选择升级包
          </el-button>
          <div v-if="selectedUpgradeFilePath" class="selected-file-path mt-3">
            <p class="text-truncate">当前选择: {{ selectedUpgradeFilePath }}</p>
          </div>
        </div>

        <div class="import-tips mt-4">
          <p class="text-warning mb-2">
            <i class="el-icon-warning-outline mr-1"></i>升级说明：
          </p>
          <ul class="ml-4">
            <li>1. 请选择压缩文件(.zip/.rar/.7z/.tar/.gz)</li>
            <li>2. 升级包必须是官方提供的版本</li>
            <li>3. 升级过程中请勿关闭系统</li>
            <li>4. 升级完成后系统将自动重启</li>
          </ul>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="upgradeDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="performUpgrade"
          :disabled="!selectedUpgradeFilePath"
          >开始升级</el-button
        >
      </span>
    </el-dialog>

    <!-- 导入组织对话框 -->
    <el-dialog
      title="导入组织信息"
      :visible.sync="importOrgDialogVisible"
      :width="dialogWidth"
      custom-class="import-dialog"
    >
      <div class="import-container">
        <div class="import-btn-container text-center mb-4">
          <el-button
            type="primary"
            @click="selectOrgFile"
            class="import-select-btn"
          >
            <i class="el-icon-folder-opened mr-2"></i>选择文件
          </el-button>
          <div v-if="selectedOrgFilePath" class="selected-file-path mt-3">
            <p class="text-truncate">当前选择: {{ selectedOrgFilePath }}</p>
          </div>
        </div>

        <div class="import-tips mt-4">
          <p class="text-warning mb-2">
            <i class="el-icon-warning-outline mr-1"></i>导入说明：
          </p>
          <ul class="ml-4">
            <li>1. 请选择Excel文件(.xlsx/.xls)或CSV文件(.csv)</li>
            <li>2. 文件必须包含组织名称、组织代码等必要字段</li>
            <li>3. 导入数据将仅在本地存储，连接网络后将自动同步</li>
            <li>4. 路径不能用中文</li>
          </ul>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importOrgDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="importOrganization"
          :disabled="!selectedOrgFilePath"
          >导 入</el-button
        >
      </span>
    </el-dialog>

    <!-- 导入角色对话框 -->
    <el-dialog
      title="导入角色信息"
      :visible.sync="importRoleDialogVisible"
      :width="dialogWidth"
      custom-class="import-dialog"
    >
      <div class="import-container">
        <div class="import-btn-container text-center mb-4">
          <el-button
            type="primary"
            @click="selectRoleFile"
            class="import-select-btn"
          >
            <i class="el-icon-folder-opened mr-2"></i>选择文件
          </el-button>
          <div v-if="selectedRoleFilePath" class="selected-file-path mt-3">
            <p class="text-truncate">当前选择: {{ selectedRoleFilePath }}</p>
          </div>
        </div>

        <div class="import-tips mt-4">
          <p class="text-warning mb-2">
            <i class="el-icon-warning-outline mr-1"></i>导入说明：
          </p>
          <ul class="ml-4">
            <li>1. 请选择Excel文件(.xlsx/.xls)或CSV文件(.csv)</li>
            <li>2. 文件必须包含角色名称、权限配置等必要字段</li>
            <li>3. 导入数据将仅在本地存储，连接网络后将自动同步</li>
            <li>4. 路径不能用中文</li>
          </ul>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="importRoleDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="importRole"
          :disabled="!selectedRoleFilePath"
          >导 入</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "AdminManageDrawer",
  inject: ["websocketService", "websocketMsg", "speakSync"],
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 升级相关
      upgradeDialogVisible: false,
      selectedUpgradeFilePath: "",

      // 导入组织相关
      importOrgDialogVisible: false,
      selectedOrgFilePath: "",

      // 导入角色相关
      importRoleDialogVisible: false,
      selectedRoleFilePath: "",

      // 响应式相关
      screenWidth: window.innerWidth,
    };
  },
  watch: {
    websocketMsg: {
      handler(newMessage) {
        if (newMessage) {
          this.handleUpgradeMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit("update:visible", value);
      },
    },
    // 响应式抽屉尺寸
    drawerSize() {
      if (this.screenWidth <= 768) {
        return '90%'; // 移动端
      } else if (this.screenWidth <= 1024) {
        return '70%'; // 平板
      } else if (this.screenWidth <= 1440) {
        return '50%'; // 小桌面
      } else {
        return '40%'; // 大桌面
      }
    },
    // 响应式对话框宽度
    dialogWidth() {
      if (this.screenWidth <= 768) {
        return '95%'; // 移动端
      } else if (this.screenWidth <= 1024) {
        return '80%'; // 平板
      } else if (this.screenWidth <= 1440) {
        return '60%'; // 小桌面
      } else {
        return '40%'; // 大桌面
      }
    },
  },
  mounted() {
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 移除监听器
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    // 处理窗口大小变化
    handleResize() {
      this.screenWidth = window.innerWidth;
    },

    // 处理升级相关的WebSocket消息
    async handleUpgradeMessages(message) {
      if (!message) return;
      if (message && message.messages) {
        switch (message.messages.type) {
          case 30502: // 系统升级响应
            // 关闭loading
            this.$loading().close();

            if (message.messages.data && message.messages.data.code === 1) {
              // 升级成功
              this.$message.success("系统升级成功！");
              if (this.speakSync) {
                await this.speakSync("系统升级成功");
              }
            } else {
              // 升级失败，显示错误信息
              const errorMsg = message.messages.data?.msg || "系统升级失败";
              this.$message.error(errorMsg);
              if (this.speakSync) {
                await this.speakSync(errorMsg);
              }
            }
            break;
          default:
            // 其他消息类型不处理
            break;
        }
      }
    },

    // 关闭抽屉前的处理
    handleDrawerClose(done) {
      this.drawerVisible = false;
      done();
    },

    // 创建管理员
    handleCreateAdmin() {
      this.drawerVisible = false;
      this.$emit("create-admin");
    },

    // 管理员列表
    handleManageAdmin() {
      this.drawerVisible = false;
      this.$emit("manage-admin");
    },

    // 系统升级
    handleSystemUpgrade() {
      this.drawerVisible = false;
      this.upgradeDialogVisible = true;
      this.selectedUpgradeFilePath = "";
    },

    // 选择升级文件
    selectUpgradeFile() {
      if (window.electronAPI) {
        window.electronAPI
          .selectImportFile({
            title: "选择升级包文件",
            filters: [
              { name: "压缩文件", extensions: ["zip", "rar", "7z", "tar", "gz"] },
            ],
            properties: ["openFile"],
          })
          .then((result) => {
            if (
              !result.canceled &&
              result.filePaths &&
              result.filePaths.length > 0
            ) {
              this.selectedUpgradeFilePath = result.filePaths[0];
              console.log("选择的升级包路径:", this.selectedUpgradeFilePath);
            }
          })
          .catch((err) => {
            console.error("选择文件出错:", err);
            this.$message.error("选择文件失败，请重试");
          });
      } else {
        console.error("无法访问Electron API");
        this.$message.error("当前环境不支持文件选择功能");
      }
    },

    // 执行升级
    performUpgrade() {
      if (!this.selectedUpgradeFilePath) {
        this.$message.warning("请先选择要升级的文件");
        return;
      }

      this.$loading({
        lock: true,
        text: "正在执行系统升级，请稍候...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 发送升级请求到后端
      let order = {
        type: 30502, // 系统升级的消息类型
        id: "systemUpgrade",
        data: {
          filepath: this.selectedUpgradeFilePath,
        },
      };

      this.websocketService.send(order);
      this.upgradeDialogVisible = false;
    },

    // 导入组织
    handleImportOrganization() {
      this.drawerVisible = false;
      this.importOrgDialogVisible = true;
      this.selectedOrgFilePath = "";
    },

    // 选择组织文件
    selectOrgFile() {
      if (window.electronAPI) {
        window.electronAPI
          .selectImportFile({
            title: "选择组织数据文件",
            filters: [
              { name: "Excel文件", extensions: ["xlsx", "xls"] },
              { name: "CSV文件", extensions: ["csv"] },
            ],
            properties: ["openFile"],
          })
          .then((result) => {
            if (
              !result.canceled &&
              result.filePaths &&
              result.filePaths.length > 0
            ) {
              this.selectedOrgFilePath = result.filePaths[0];
              console.log("选择的组织文件路径:", this.selectedOrgFilePath);
            }
          })
          .catch((err) => {
            console.error("选择文件出错:", err);
            this.$message.error("选择文件失败，请重试");
          });
      } else {
        console.error("无法访问Electron API");
        this.$message.error("当前环境不支持文件选择功能");
      }
    },

    // 导入组织数据
    importOrganization() {
      if (!this.selectedOrgFilePath) {
        this.$message.warning("请先选择要导入的文件");
        return;
      }

      this.$loading({
        lock: true,
        text: "正在导入组织数据，请稍候...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 发送导入请求到后端
      let order = {
        type: 10099, // 假设导入组织的消息类型
        id: "importOrganization",
        data: {
          path: this.selectedOrgFilePath,
        },
      };

      this.websocketService.send(order);
      this.importOrgDialogVisible = false;
    },

    // 导入角色
    handleImportRole() {
      this.drawerVisible = false;
      this.importRoleDialogVisible = true;
      this.selectedRoleFilePath = "";
    },

    // 选择角色文件
    selectRoleFile() {
      if (window.electronAPI) {
        window.electronAPI
          .selectImportFile({
            title: "选择角色数据文件",
            filters: [
              { name: "Excel文件", extensions: ["xlsx", "xls"] },
              { name: "CSV文件", extensions: ["csv"] },
            ],
            properties: ["openFile"],
          })
          .then((result) => {
            if (
              !result.canceled &&
              result.filePaths &&
              result.filePaths.length > 0
            ) {
              this.selectedRoleFilePath = result.filePaths[0];
              console.log("选择的角色文件路径:", this.selectedRoleFilePath);
            }
          })
          .catch((err) => {
            console.error("选择文件出错:", err);
            this.$message.error("选择文件失败，请重试");
          });
      } else {
        console.error("无法访问Electron API");
        this.$message.error("当前环境不支持文件选择功能");
      }
    },

    // 导入角色数据
    importRole() {
      if (!this.selectedRoleFilePath) {
        this.$message.warning("请先选择要导入的文件");
        return;
      }

      this.$loading({
        lock: true,
        text: "正在导入角色数据，请稍候...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 发送导入请求到后端
      let order = {
        type: 10100, // 假设导入角色的消息类型
        id: "importRole",
        data: {
          path: this.selectedRoleFilePath,
        },
      };

      this.websocketService.send(order);
      this.importRoleDialogVisible = false;
    },
  },
};
</script>

<style scoped>
/* 管理员抽屉样式 */
:deep(.admin-drawer) {
  background-color: #1a1a2e !important;
}

:deep(.admin-drawer .el-drawer__header) {
  background: linear-gradient(45deg, #0066ff, #00a3ff);
  color: white;
  padding: 20px;
  margin-bottom: 0;
}

:deep(.admin-drawer .el-drawer__title) {
  color: white !important;
  font-weight: bold;
  font-size: 18px;
}

/* 抽屉标题响应式 */
@media (max-width: 768px) {
  :deep(.admin-drawer .el-drawer__title) {
    font-size: 16px !important;
  }

  :deep(.admin-drawer .el-drawer__header) {
    padding: 15px !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  :deep(.admin-drawer .el-drawer__title) {
    font-size: 17px !important;
  }

  :deep(.admin-drawer .el-drawer__header) {
    padding: 18px !important;
  }
}

@media (min-width: 1441px) {
  :deep(.admin-drawer .el-drawer__title) {
    font-size: 20px !important;
  }

  :deep(.admin-drawer .el-drawer__header) {
    padding: 25px !important;
  }
}

:deep(.admin-drawer .el-drawer__close-btn) {
  color: white !important;
  font-size: 20px;
}

:deep(.admin-drawer .el-drawer__close-btn:hover) {
  color: #e2e8f0 !important;
}

:deep(.admin-drawer .el-drawer__body) {
  padding: 0;
  background-color: #1a1a2e;
}

.drawer-content {
  padding: 20px;
  height: 100%;
  background-color: #1a1a2e;
}

.drawer-title {
  color: #fff;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 30px;
  text-align: center;
  padding-bottom: 15px;
  border-bottom: 2px solid #0066ff;
}

.drawer-menu {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background: linear-gradient(135deg, #16213e, #1a1a2e);
  border: 1px solid #263566;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #fff;
}

.menu-item:hover {
  background: linear-gradient(135deg, #0066ff, #00a3ff);
  border-color: #00a3ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 102, 255, 0.3);
}

.menu-item i {
  font-size: 18px;
  margin-right: 15px;
  width: 20px;
  text-align: center;
}

.menu-item span {
  font-size: 14px;
  font-weight: 500;
}

/* 响应式设计 - 移动端 */
@media (max-width: 768px) {
  .drawer-content {
    padding: 15px;
  }

  .drawer-title {
    font-size: 18px;
    margin-bottom: 20px;
  }

  .drawer-menu {
    gap: 12px;
  }

  .menu-item {
    padding: 12px 15px;
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .menu-item i {
    font-size: 24px;
    margin-right: 0;
    margin-bottom: 5px;
  }

  .menu-item span {
    font-size: 12px;
  }

  .import-container {
    padding: 15px;
  }

  .import-select-btn {
    width: 100% !important;
    max-width: 200px;
  }
}

/* 响应式设计 - 平板 */
@media (min-width: 769px) and (max-width: 1024px) {
  .drawer-content {
    padding: 18px;
  }

  .drawer-title {
    font-size: 19px;
    margin-bottom: 25px;
  }

  .menu-item {
    padding: 13px 18px;
  }

  .menu-item i {
    font-size: 17px;
    margin-right: 12px;
  }

  .menu-item span {
    font-size: 13px;
  }

  .import-container {
    padding: 18px;
  }
}

/* 响应式设计 - 小桌面 */
@media (min-width: 1025px) and (max-width: 1440px) {
  .drawer-content {
    padding: 20px;
  }

  .menu-item {
    padding: 14px 18px;
  }

  .menu-item i {
    font-size: 17px;
    margin-right: 13px;
  }

  .menu-item span {
    font-size: 13px;
  }
}

/* 响应式设计 - 大桌面 */
@media (min-width: 1441px) {
  .drawer-content {
    padding: 25px;
  }

  .drawer-title {
    font-size: 22px;
    margin-bottom: 35px;
  }

  .menu-item {
    padding: 18px 25px;
  }

  .menu-item i {
    font-size: 20px;
    margin-right: 18px;
  }

  .menu-item span {
    font-size: 15px;
  }
}

/* 导入对话框样式 */
:deep(.import-dialog) {
  border-radius: 4px !important;
  overflow: hidden !important;
}

:deep(.import-dialog .el-dialog) {
  background-color: #1c1f37 !important;
  border: 1px solid #263566 !important;
  border-radius: 4px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
  overflow: hidden !important;
}

:deep(.import-dialog .el-dialog__header) {
  background-color: #263566 !important;
  padding: 15px 20px !important;
  border-bottom: 1px solid #263566 !important;
}

:deep(.import-dialog .el-dialog__title) {
  color: #fff !important;
  font-weight: bold !important;
}

:deep(.import-dialog .el-dialog__body) {
  color: #fff !important;
  padding: 0 !important;
}

:deep(.import-dialog .el-dialog__footer) {
  border-top: 1px solid #263566 !important;
  padding: 15px 20px !important;
}

.import-container {
  padding: 20px;
}

.import-tips {
  background-color: rgba(255, 229, 100, 0.1);
  border-left: 4px solid #e6a23c;
  padding: 10px 15px;
  border-radius: 0 4px 4px 0;
  color: #fff;
}

.import-tips ul {
  color: #fff;
  margin: 0;
  padding-left: 1rem;
}

.import-tips li {
  color: #fff;
  margin-bottom: 4px;
  line-height: 1.5;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.import-select-btn {
  width: 180px;
  background-color: #3f68cd !important;
  border-color: #3f68cd !important;
}

.selected-file-path {
  color: #67c23a;
  word-break: break-all;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-center {
  text-align: center;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.ml-4 {
  margin-left: 1rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

/* 对话框响应式设计 */
@media (max-width: 768px) {
  :deep(.import-dialog .el-dialog) {
    margin: 5vh auto !important;
    max-height: 90vh !important;
  }

  :deep(.import-dialog .el-dialog__header) {
    padding: 12px 15px !important;
  }

  :deep(.import-dialog .el-dialog__title) {
    font-size: 16px !important;
  }

  :deep(.import-dialog .el-dialog__footer) {
    padding: 12px 15px !important;
  }

  .import-tips {
    padding: 8px 12px;
    font-size: 13px;
  }

  .import-tips ul li {
    font-size: 12px;
    margin-bottom: 3px;
    color: #fff !important;
  }

  .selected-file-path p {
    font-size: 12px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  :deep(.import-dialog .el-dialog) {
    margin: 8vh auto !important;
  }

  :deep(.import-dialog .el-dialog__header) {
    padding: 14px 18px !important;
  }

  :deep(.import-dialog .el-dialog__title) {
    font-size: 17px !important;
  }

  .import-tips {
    padding: 9px 13px;
    font-size: 14px;
  }

  .import-tips ul li {
    font-size: 13px;
  }
}

@media (min-width: 1441px) {
  :deep(.import-dialog .el-dialog) {
    margin: 10vh auto !important;
  }

  :deep(.import-dialog .el-dialog__header) {
    padding: 18px 25px !important;
  }

  :deep(.import-dialog .el-dialog__title) {
    font-size: 19px !important;
  }

  :deep(.import-dialog .el-dialog__footer) {
    padding: 18px 25px !important;
  }

  .import-container {
    padding: 25px;
  }

  .import-select-btn {
    width: 200px !important;
  }

  .import-tips {
    padding: 12px 18px;
    font-size: 15px;
  }

  .import-tips ul li {
    font-size: 14px;
  }
}
</style>
